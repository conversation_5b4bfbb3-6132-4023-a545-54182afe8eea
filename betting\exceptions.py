"""
Custom exceptions for the betting application
"""

class BettingException(Exception):
    """Base exception for all betting-related errors"""
    default_message = "An error occurred during the betting operation"
    
    def __init__(self, message=None, *args, **kwargs):
        self.message = message or self.default_message
        super().__init__(self.message, *args, **kwargs)


class InsufficientBalanceException(BettingException):
    """Exception raised when user has insufficient balance for a bet"""
    default_message = "Insufficient balance to place this bet"


class InvalidStakeException(BettingException):
    """Exception raised when stake amount is invalid"""
    default_message = "Invalid stake amount"


class InvalidOddsException(BettingException):
    """Exception raised when odds have changed or are invalid"""
    default_message = "Odds have changed or are no longer valid"


class EventStartedException(BettingException):
    """Exception raised when trying to bet on an event that has already started"""
    default_message = "Cannot place bet on an event that has already started"


class MaximumStakeExceededException(BettingException):
    """Exception raised when stake exceeds maximum allowed"""
    default_message = "Stake exceeds maximum allowed amount"


class MinimumStakeException(BettingException):
    """Exception raised when stake is below minimum allowed"""
    default_message = "Stake is below minimum allowed amount"


class MaximumSelectionsExceededException(BettingException):
    """Exception raised when too many selections are added to a bet"""
    default_message = "Maximum number of selections exceeded"


class BetPlacementException(BettingException):
    """Exception raised when bet cannot be placed due to general error"""
    default_message = "Unable to place bet at this time"


class BetCancellationException(BettingException):
    """Exception raised when bet cannot be cancelled"""
    default_message = "This bet cannot be cancelled"


class MarketSuspendedException(BettingException):
    """Exception raised when market is suspended"""
    default_message = "This market is currently suspended"


class DuplicateSelectionException(BettingException):
    """Exception raised when trying to add duplicate selections"""
    default_message = "Cannot add duplicate selections to the same bet"


class IncompatibleSelectionsException(BettingException):
    """Exception raised when selections are incompatible (e.g., from same event in a multi-bet)"""
    default_message = "Incompatible selections cannot be combined in the same bet"


class BetNotFoundException(BettingException):
    """Exception raised when bet is not found"""
    default_message = "Bet not found"


class BetAlreadySettledException(BettingException):
    """Exception raised when trying to modify a settled bet"""
    default_message = "Cannot modify a bet that has already been settled"


class InvalidSelectionException(BettingException):
    """Exception raised when selection is invalid"""
    default_message = "Invalid selection"
