"""
WebSocket consumers for live betting features
"""

import json
import logging
from datetime import datetime
from decimal import Decimal
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger(__name__)
User = get_user_model()


class LiveOddsConsumer(AsyncWebsocketConsumer):
    """Consumer for live odds updates with authentication and connection management"""

    async def connect(self):
        """Handle WebSocket connection"""
        # Get user from scope (set by AuthMiddlewareStack)
        self.user = self.scope.get("user")

        # Check if user is authenticated
        if self.user is None or isinstance(self.user, AnonymousUser):
            logger.warning("Unauthenticated user attempted to connect to live odds")
            await self.close(code=4001)  # Custom close code for authentication failure
            return

        # Set up connection tracking
        self.room_group_name = 'live_odds'
        self.user_id = str(self.user.id)
        self.connection_time = timezone.now()

        # Track active connections
        await self.track_connection(True)

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        # Accept connection
        await self.accept()

        # Send connection confirmation
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'user_id': self.user_id,
            'timestamp': self.connection_time.isoformat(),
            'message': 'Connected to live odds feed'
        }))

        logger.info(f"User {self.user_id} connected to live odds")

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if hasattr(self, 'user') and self.user and not isinstance(self.user, AnonymousUser):
            # Track disconnection
            await self.track_connection(False)

            # Leave room group
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

            logger.info(f"User {self.user_id} disconnected from live odds (code: {close_code})")

    async def receive(self, text_data):
        """Handle messages from WebSocket"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')

            if message_type == 'subscribe_odds':
                await self.handle_odds_subscription(text_data_json)
            elif message_type == 'unsubscribe_odds':
                await self.handle_odds_unsubscription(text_data_json)
            elif message_type == 'ping':
                await self.handle_ping()
            else:
                await self.send_error('Unknown message type')

        except json.JSONDecodeError:
            await self.send_error('Invalid JSON format')
        except Exception as e:
            logger.error(f"Error processing message from user {self.user_id}: {e}")
            await self.send_error('Internal server error')

    async def handle_odds_subscription(self, data):
        """Handle odds subscription request"""
        sport_ids = data.get('sport_ids', [])
        event_ids = data.get('event_ids', [])

        # Store subscription preferences in cache
        cache_key = f"odds_subscription_{self.user_id}"
        subscription_data = {
            'sport_ids': sport_ids,
            'event_ids': event_ids,
            'subscribed_at': timezone.now().isoformat()
        }
        cache.set(cache_key, subscription_data, timeout=3600)  # 1 hour

        await self.send(text_data=json.dumps({
            'type': 'odds_subscribed',
            'sport_ids': sport_ids,
            'event_ids': event_ids,
            'message': 'Successfully subscribed to live odds'
        }))

        logger.info(f"User {self.user_id} subscribed to odds: sports={sport_ids}, events={event_ids}")

    async def handle_odds_unsubscription(self, data):
        """Handle odds unsubscription request"""
        # Remove subscription from cache
        cache_key = f"odds_subscription_{self.user_id}"
        cache.delete(cache_key)

        await self.send(text_data=json.dumps({
            'type': 'odds_unsubscribed',
            'message': 'Successfully unsubscribed from live odds'
        }))

        logger.info(f"User {self.user_id} unsubscribed from odds")

    async def handle_ping(self):
        """Handle ping message for connection health check"""
        await self.send(text_data=json.dumps({
            'type': 'pong',
            'timestamp': timezone.now().isoformat()
        }))

    async def send_error(self, message):
        """Send error message to client"""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': message,
            'timestamp': timezone.now().isoformat()
        }))

    async def odds_update(self, event):
        """Send odds update to WebSocket"""
        # Check if user is subscribed to this update
        if await self.should_send_update(event):
            await self.send(text_data=json.dumps({
                'type': 'odds_update',
                'data': event['data'],
                'timestamp': timezone.now().isoformat()
            }))

    async def should_send_update(self, event):
        """Check if user should receive this odds update"""
        cache_key = f"odds_subscription_{self.user_id}"
        subscription_data = cache.get(cache_key)

        if not subscription_data:
            return True  # Send all updates if no specific subscription

        event_data = event.get('data', {})
        sport_id = event_data.get('sport_id')
        event_id = event_data.get('event_id')

        # Check if user is subscribed to this sport or event
        subscribed_sports = subscription_data.get('sport_ids', [])
        subscribed_events = subscription_data.get('event_ids', [])

        if subscribed_events and event_id in subscribed_events:
            return True
        if subscribed_sports and sport_id in subscribed_sports:
            return True

        return False

    @database_sync_to_async
    def track_connection(self, connected):
        """Track user connection status"""
        try:
            # Update user's last activity
            if connected:
                cache.set(f"user_online_{self.user_id}", True, timeout=300)  # 5 minutes
                # Increment active connections count
                current_count = cache.get('active_connections_count', 0)
                cache.set('active_connections_count', current_count + 1, timeout=None)
            else:
                cache.delete(f"user_online_{self.user_id}")
                # Decrement active connections count
                current_count = cache.get('active_connections_count', 0)
                if current_count > 0:
                    cache.set('active_connections_count', current_count - 1, timeout=None)
        except Exception as e:
            logger.error(f"Error tracking connection for user {self.user_id}: {e}")


class LiveBettingConsumer(AsyncWebsocketConsumer):
    """Consumer for live betting on specific events with enhanced functionality"""

    async def connect(self):
        """Handle WebSocket connection for specific event"""
        # Get user from scope
        self.user = self.scope.get("user")

        # Check authentication
        if self.user is None or isinstance(self.user, AnonymousUser):
            logger.warning("Unauthenticated user attempted to connect to live betting")
            await self.close(code=4001)
            return

        # Get event ID from URL
        self.event_id = self.scope['url_route']['kwargs']['event_id']
        self.user_id = str(self.user.id)
        self.room_group_name = f'live_betting_{self.event_id}'
        self.connection_time = timezone.now()

        # Validate event exists and is live
        event_valid = await self.validate_event()
        if not event_valid:
            await self.close(code=4004)  # Event not found or not live
            return

        # Track connection
        await self.track_event_connection(True)

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

        # Send connection confirmation with event details
        event_details = await self.get_event_details()
        await self.send(text_data=json.dumps({
            'type': 'event_joined',
            'event_id': self.event_id,
            'user_id': self.user_id,
            'event_details': event_details,
            'timestamp': self.connection_time.isoformat(),
            'message': f'Joined live betting for event {self.event_id}'
        }))

        logger.info(f"User {self.user_id} joined live betting for event {self.event_id}")

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if hasattr(self, 'user') and self.user and not isinstance(self.user, AnonymousUser):
            # Track disconnection
            await self.track_event_connection(False)

            # Leave room group
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

            logger.info(f"User {self.user_id} left live betting for event {self.event_id} (code: {close_code})")

    async def receive(self, text_data):
        """Handle messages from WebSocket"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')

            if message_type == 'place_bet':
                await self.handle_place_bet(text_data_json)
            elif message_type == 'get_event_status':
                await self.handle_get_event_status()
            elif message_type == 'get_live_odds':
                await self.handle_get_live_odds()
            elif message_type == 'ping':
                await self.handle_ping()
            else:
                await self.send_error('Unknown message type')

        except json.JSONDecodeError:
            await self.send_error('Invalid JSON format')
        except Exception as e:
            logger.error(f"Error processing message from user {self.user_id} for event {self.event_id}: {e}")
            await self.send_error('Internal server error')

    async def handle_place_bet(self, data):
        """Handle live bet placement"""
        try:
            market_id = data.get('market_id')
            odds_id = data.get('odds_id')
            stake = Decimal(str(data.get('stake', 0)))

            if not all([market_id, odds_id, stake > 0]):
                await self.send_error('Invalid bet data')
                return

            # Validate bet placement
            bet_result = await self.process_live_bet(market_id, odds_id, stake)

            if bet_result['success']:
                await self.send(text_data=json.dumps({
                    'type': 'bet_placed',
                    'bet_id': bet_result['bet_id'],
                    'market_id': market_id,
                    'odds_id': odds_id,
                    'stake': str(stake),
                    'potential_winnings': str(bet_result['potential_winnings']),
                    'timestamp': timezone.now().isoformat(),
                    'message': 'Bet placed successfully'
                }))
            else:
                await self.send_error(bet_result['error'])

        except (ValueError, TypeError) as e:
            await self.send_error('Invalid stake amount')
        except Exception as e:
            logger.error(f"Error placing bet for user {self.user_id}: {e}")
            await self.send_error('Failed to place bet')

    async def handle_get_event_status(self):
        """Send current event status"""
        event_status = await self.get_event_status()
        await self.send(text_data=json.dumps({
            'type': 'event_status',
            'event_id': self.event_id,
            'status': event_status,
            'timestamp': timezone.now().isoformat()
        }))

    async def handle_get_live_odds(self):
        """Send current live odds for the event"""
        live_odds = await self.get_live_odds()
        await self.send(text_data=json.dumps({
            'type': 'live_odds',
            'event_id': self.event_id,
            'odds': live_odds,
            'timestamp': timezone.now().isoformat()
        }))

    async def handle_ping(self):
        """Handle ping for connection health check"""
        await self.send(text_data=json.dumps({
            'type': 'pong',
            'event_id': self.event_id,
            'timestamp': timezone.now().isoformat()
        }))

    async def send_error(self, message):
        """Send error message to client"""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'event_id': self.event_id,
            'message': message,
            'timestamp': timezone.now().isoformat()
        }))

    # Event handlers for group messages
    async def event_update(self, event):
        """Send event update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'event_update',
            'event_id': self.event_id,
            'data': event['data'],
            'timestamp': timezone.now().isoformat()
        }))

    async def odds_change(self, event):
        """Send odds change notification"""
        await self.send(text_data=json.dumps({
            'type': 'odds_change',
            'event_id': self.event_id,
            'data': event['data'],
            'timestamp': timezone.now().isoformat()
        }))

    async def bet_update(self, event):
        """Send bet update notification"""
        # Only send to the user who placed the bet
        if event.get('user_id') == self.user_id:
            await self.send(text_data=json.dumps({
                'type': 'bet_update',
                'event_id': self.event_id,
                'data': event['data'],
                'timestamp': timezone.now().isoformat()
            }))

    async def event_finished(self, event):
        """Send event finished notification"""
        await self.send(text_data=json.dumps({
            'type': 'event_finished',
            'event_id': self.event_id,
            'data': event['data'],
            'timestamp': timezone.now().isoformat()
        }))

    # Database operations
    @database_sync_to_async
    def validate_event(self):
        """Validate that event exists and is available for live betting"""
        try:
            from sports.models import Event
            event = Event.objects.get(id=self.event_id, status='live')
            return True
        except Event.DoesNotExist:
            return False

    @database_sync_to_async
    def get_event_details(self):
        """Get event details for connection confirmation"""
        try:
            from sports.models import Event
            event = Event.objects.select_related('sport').get(id=self.event_id)
            return {
                'name': f"{event.home_team} vs {event.away_team}",
                'sport': event.sport.name,
                'start_time': event.start_time.isoformat(),
                'status': event.status,
                'is_live': event.is_live
            }
        except Exception:
            return {}

    @database_sync_to_async
    def get_event_status(self):
        """Get current event status"""
        try:
            from sports.models import Event
            event = Event.objects.get(id=self.event_id)
            return {
                'status': event.status,
                'is_live': event.is_live,
                'home_score': event.home_score,
                'away_score': event.away_score,
                'match_time': event.match_time
            }
        except Exception:
            return {}

    @database_sync_to_async
    def get_live_odds(self):
        """Get current live odds for the event"""
        try:
            from sports.models import Odds, Market
            markets = Market.objects.filter(event_id=self.event_id, is_active=True)
            odds_data = []

            for market in markets:
                odds = Odds.objects.filter(market=market, is_active=True)
                market_odds = {
                    'market_id': str(market.pk),
                    'market_name': market.name,
                    'odds': [
                        {
                            'id': str(odd.pk),
                            'name': odd.selection,
                            'value': str(odd.odds_value),
                            'updated_at': odd.last_updated.isoformat()
                        }
                        for odd in odds
                    ]
                }
                odds_data.append(market_odds)

            return odds_data
        except Exception as e:
            logger.error(f"Error getting live odds for event {self.event_id}: {e}")
            return []

    @database_sync_to_async
    def process_live_bet(self, market_id, odds_id, stake):
        """Process live bet placement"""
        try:
            from sports.models import Odds, Market
            from betting.services import BettingService

            # Get odds and validate
            odds = Odds.objects.get(id=odds_id, market_id=market_id, is_active=True)
            market = Market.objects.get(id=market_id, event_id=self.event_id, is_active=True)

            # Check if event is still accepting bets
            if not market.event.is_live or market.event.status == 'finished':
                return {'success': False, 'error': 'Event is no longer accepting bets'}

            # Use betting service to place bet
            betting_service = BettingService()
            bet_result = betting_service.place_bet(
                user=self.user,
                selections=[{
                    'market_id': market_id,
                    'odds_id': odds_id,
                    'odds_value': odds.value
                }],
                stake=stake,
                bet_type='single'
            )

            return {
                'success': True,
                'bet_id': str(bet_result.id),
                'potential_winnings': bet_result.potential_winnings
            }

        except Exception as e:
            logger.error(f"Error processing live bet: {e}")
            return {'success': False, 'error': 'Failed to place bet'}

    @database_sync_to_async
    def track_event_connection(self, connected):
        """Track user connection to specific event"""
        try:
            cache_key = f"event_connections_{self.event_id}"
            connections = cache.get(cache_key, set())

            if connected:
                connections.add(self.user_id)
            else:
                connections.discard(self.user_id)

            cache.set(cache_key, connections, timeout=3600)  # 1 hour

            # Update connection count for this event
            count_key = f"event_connection_count_{self.event_id}"
            cache.set(count_key, len(connections), timeout=3600)

        except Exception as e:
            logger.error(f"Error tracking event connection: {e}")