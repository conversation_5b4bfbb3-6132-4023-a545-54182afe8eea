"""
Jackpot services for game logic, prize calculation, and winner determination
"""

import logging
from decimal import Decimal
from typing import Dict, List, Tuple, Optional, TYPE_CHECKING
from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model

from .models import Jackpot, JackpotGame, JackpotEntry, JackpotPrediction, JackpotWinner
from sports.models import Event, Market, Odds

if TYPE_CHECKING:
    from accounts.models import CustomUser

logger = logging.getLogger(__name__)
User = get_user_model()


class JackpotService:
    """Service for managing jackpot operations"""
    
    def create_jackpot(self, name: str, events: List[Event], **kwargs) -> Jackpot:
        """
        Create a new jackpot with specified events
        
        Args:
            name: Jackpot name
            events: List of events to include
            **kwargs: Additional jackpot parameters
        
        Returns:
            Jackpot: Created jackpot instance
        """
        try:
            with transaction.atomic():
                # Create jackpot
                jackpot = Jackpot.objects.create(
                    name=name,
                    total_games=len(events),
                    **kwargs
                )
                
                # Add base prize pool to current pool
                jackpot.current_prize_pool = jackpot.base_prize_pool
                jackpot.save(update_fields=['current_prize_pool'])
                
                # Create jackpot games
                for i, event in enumerate(events, 1):
                    # Get the main market for the event (usually Match Winner)
                    market = self._get_main_market_for_event(event)
                    
                    JackpotGame.objects.create(
                        jackpot=jackpot,
                        event=event,
                        market=market,
                        game_number=i
                    )
                
                logger.info(f"Created jackpot '{name}' with {len(events)} games")
                return jackpot
                
        except Exception as e:
            logger.error(f"Error creating jackpot: {e}")
            raise
    
    def _get_main_market_for_event(self, event: Event) -> Market:
        """Get the main market for an event (usually Match Winner)"""
        # Try to find Match Winner market first
        market = Market.objects.filter(
            event=event,
            name__icontains='winner',
            is_active=True
        ).first()
        
        if not market:
            # Fall back to first active market
            market = Market.objects.filter(
                event=event,
                is_active=True
            ).first()
        
        if not market:
            raise ValueError(f"No active market found for event {event.pk}")
        
        return market
    
    def enter_jackpot(self, user: "CustomUser", jackpot: Jackpot, predictions: Dict[str, str]) -> JackpotEntry:
        """
        Enter a user into a jackpot with their predictions
        
        Args:
            user: User entering the jackpot
            jackpot: Jackpot to enter
            predictions: Dict mapping game_id to odds_id
        
        Returns:
            JackpotEntry: Created entry
        """
        try:
            with transaction.atomic():
                # Validate jackpot is open
                if not jackpot.is_open_for_entries:
                    raise ValueError("Jackpot is not open for entries")
                
                # Check if user already has an entry
                if JackpotEntry.objects.filter(jackpot=jackpot, user=user).exists():
                    raise ValueError("User already has an entry in this jackpot")
                
                # Validate user has sufficient balance
                if user.balance < jackpot.entry_fee:
                    raise ValueError("Insufficient balance for entry fee")
                
                # Validate predictions
                games = jackpot.games.all()
                if len(predictions) != games.count():
                    raise ValueError(f"Must provide predictions for all {games.count()} games")
                
                # Deduct entry fee from user balance
                user.balance -= jackpot.entry_fee
                user.save(update_fields=['balance'])
                
                # Create transaction record
                from payments.models import Transaction
                Transaction.objects.create(
                    user=user,
                    transaction_type='debit',
                    amount=jackpot.entry_fee,
                    status='completed',
                    description=f'Jackpot entry fee - {jackpot.name}',
                    metadata={
                        'jackpot_id': str(jackpot.id),
                        'jackpot_name': jackpot.name
                    }
                )
                
                # Create jackpot entry
                entry = JackpotEntry.objects.create(
                    jackpot=jackpot,
                    user=user,
                    entry_fee_paid=jackpot.entry_fee
                )
                
                # Create predictions
                for game in games:
                    game_id = str(game.id)
                    odds_id = predictions.get(game_id)
                    
                    if not odds_id:
                        raise ValueError(f"Missing prediction for game {game.game_number}")
                    
                    try:
                        odds = Odds.objects.get(
                            id=odds_id,
                            market=game.market,
                            is_active=True
                        )
                    except Odds.DoesNotExist:
                        raise ValueError(f"Invalid odds selection for game {game.game_number}")
                    
                    JackpotPrediction.objects.create(
                        entry=entry,
                        game=game,
                        predicted_odds=odds
                    )
                
                # Add entry fee to prize pool (minus platform fee)
                platform_fee_percentage = Decimal('10.00')  # 10% platform fee
                prize_pool_contribution = jackpot.entry_fee * (Decimal('100.00') - platform_fee_percentage) / Decimal('100.00')
                jackpot.add_to_prize_pool(prize_pool_contribution)
                
                # Update jackpot statistics
                jackpot.total_entries += 1
                if jackpot.entries.filter(user=user).count() == 1:  # First entry for this user
                    jackpot.total_participants += 1
                jackpot.save(update_fields=['total_entries', 'total_participants'])
                
                logger.info(f"User {user.pk} entered jackpot {jackpot.id}")
                return entry
                
        except Exception as e:
            logger.error(f"Error entering jackpot: {e}")
            raise
    
    def settle_jackpot_game(self, game: JackpotGame, winning_odds: Odds) -> int:
        """
        Settle a jackpot game and update all related predictions
        
        Args:
            game: JackpotGame to settle
            winning_odds: Winning odds for the game
        
        Returns:
            int: Number of correct predictions
        """
        try:
            with transaction.atomic():
                # Update game with winning odds
                game.winning_odds = winning_odds
                game.is_settled = True
                game.settled_at = timezone.now()
                game.save(update_fields=['winning_odds', 'is_settled', 'settled_at'])
                
                # Update all predictions for this game
                predictions = JackpotPrediction.objects.filter(game=game)
                correct_count = 0
                
                for prediction in predictions:
                    if prediction.check_result():
                        correct_count += 1
                
                logger.info(f"Settled jackpot game {game.id}, {correct_count} correct predictions")
                
                # Check if jackpot is ready for final settlement
                if game.jackpot.is_ready_for_settlement():
                    self.settle_jackpot(game.jackpot)
                
                return correct_count
                
        except Exception as e:
            logger.error(f"Error settling jackpot game: {e}")
            raise
    
    def settle_jackpot(self, jackpot: Jackpot) -> Dict[str, int]:
        """
        Settle the entire jackpot and distribute prizes
        
        Args:
            jackpot: Jackpot to settle
        
        Returns:
            dict: Settlement summary with winner counts
        """
        try:
            with transaction.atomic():
                # Get all entries sorted by correct predictions (descending)
                entries = jackpot.entries.filter(status='active').order_by(
                    '-correct_predictions', 'created_at'
                )
                
                if not entries.exists():
                    logger.warning(f"No active entries found for jackpot {jackpot.id}")
                    jackpot.status = 'settled'
                    jackpot.settlement_time = timezone.now()
                    jackpot.save(update_fields=['status', 'settlement_time'])
                    return {'winners': 0}
                
                # Determine winners based on correct predictions
                winners_data = self._determine_winners(entries, jackpot)
                
                # Create winner records and distribute prizes
                total_winners = 0
                for tier, tier_winners in winners_data.items():
                    for entry in tier_winners:
                        prize_amount = self._calculate_prize_amount(jackpot, tier, len(tier_winners))
                        
                        # Create winner record
                        winner = JackpotWinner.objects.create(
                            jackpot=jackpot,
                            entry=entry,
                            user=entry.user,
                            prize_tier=tier,
                            prize_amount=prize_amount
                        )
                        
                        # Update entry
                        entry.is_winner = True
                        entry.prize_won = prize_amount
                        entry.prize_tier = tier
                        entry.status = 'settled'
                        entry.settled_at = timezone.now()
                        entry.save(update_fields=[
                            'is_winner', 'prize_won', 'prize_tier', 'status', 'settled_at'
                        ])
                        
                        # Pay the prize
                        winner.pay_prize()
                        total_winners += 1
                
                # Update non-winning entries
                non_winning_entries = entries.exclude(
                    id__in=[entry.id for tier_winners in winners_data.values() for entry in tier_winners]
                )
                non_winning_entries.update(
                    status='settled',
                    settled_at=timezone.now()
                )
                
                # Update jackpot status
                jackpot.status = 'settled'
                jackpot.settlement_time = timezone.now()
                jackpot.save(update_fields=['status', 'settlement_time'])
                
                logger.info(f"Settled jackpot {jackpot.id} with {total_winners} winners")
                
                return {
                    'winners': total_winners,
                    'first_prize': len(winners_data.get('first', [])),
                    'second_prize': len(winners_data.get('second', [])),
                    'third_prize': len(winners_data.get('third', [])),
                    'consolation': len(winners_data.get('consolation', []))
                }
                
        except Exception as e:
            logger.error(f"Error settling jackpot: {e}")
            raise
    
    def _determine_winners(self, entries, jackpot: Jackpot) -> Dict[str, List[JackpotEntry]]:
        """Determine winners based on correct predictions"""
        winners = {
            'first': [],
            'second': [],
            'third': [],
            'consolation': []
        }
        
        # Group entries by correct predictions count
        prediction_groups = {}
        for entry in entries:
            count = entry.correct_predictions
            if count not in prediction_groups:
                prediction_groups[count] = []
            prediction_groups[count].append(entry)
        
        # Sort prediction counts in descending order
        sorted_counts = sorted(prediction_groups.keys(), reverse=True)
        
        # Assign prizes based on performance
        assigned_tiers = []
        
        for count in sorted_counts:
            entries_with_count = prediction_groups[count]
            
            # Check if this score qualifies for any prize
            if count >= jackpot.minimum_correct_predictions:
                # Assign highest available tier
                if not assigned_tiers or 'first' not in assigned_tiers:
                    winners['first'] = entries_with_count
                    assigned_tiers.append('first')
                elif 'second' not in assigned_tiers:
                    winners['second'] = entries_with_count
                    assigned_tiers.append('second')
                elif 'third' not in assigned_tiers:
                    winners['third'] = entries_with_count
                    assigned_tiers.append('third')
                else:
                    # Consolation prize for remaining qualifiers
                    winners['consolation'].extend(entries_with_count)
            
            # Stop if we've assigned all main tiers
            if len(assigned_tiers) >= 3:
                break
        
        return winners
    
    def _calculate_prize_amount(self, jackpot: Jackpot, tier: str, winner_count: int) -> Decimal:
        """Calculate prize amount for a specific tier"""
        if tier == 'first':
            total_tier_amount = jackpot.first_prize_amount
        elif tier == 'second':
            total_tier_amount = jackpot.second_prize_amount
        elif tier == 'third':
            total_tier_amount = jackpot.third_prize_amount
        else:  # consolation
            # Consolation gets remaining amount
            allocated = jackpot.first_prize_amount + jackpot.second_prize_amount + jackpot.third_prize_amount
            total_tier_amount = jackpot.current_prize_pool - allocated
        
        # Divide equally among winners in this tier
        if winner_count > 0:
            return total_tier_amount / Decimal(str(winner_count))
        return Decimal('0.00')
    
    def get_jackpot_statistics(self, jackpot: Jackpot) -> Dict:
        """Get comprehensive statistics for a jackpot"""
        try:
            stats = {
                'jackpot_id': str(jackpot.id),
                'name': jackpot.name,
                'status': jackpot.status,
                'total_entries': jackpot.total_entries,
                'total_participants': jackpot.total_participants,
                'current_prize_pool': str(jackpot.current_prize_pool),
                'entry_fee': str(jackpot.entry_fee),
                'total_games': jackpot.total_games,
                'completed_games': jackpot.get_completed_games_count(),
                'is_open': jackpot.is_open_for_entries,
                'start_time': jackpot.start_time.isoformat(),
                'end_time': jackpot.end_time.isoformat(),
            }
            
            # Add prize breakdown
            stats['prizes'] = {
                'first': str(jackpot.first_prize_amount),
                'second': str(jackpot.second_prize_amount),
                'third': str(jackpot.third_prize_amount),
            }
            
            # Add winner statistics if settled
            if jackpot.status == 'settled':
                winners = jackpot.winners.all()
                stats['winners'] = {
                    'total': winners.count(),
                    'first_prize': winners.filter(prize_tier='first').count(),
                    'second_prize': winners.filter(prize_tier='second').count(),
                    'third_prize': winners.filter(prize_tier='third').count(),
                    'consolation': winners.filter(prize_tier='consolation').count(),
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting jackpot statistics: {e}")
            return {'error': str(e)}
    
    def get_user_entry_status(self, user: "CustomUser", jackpot: Jackpot) -> Optional[Dict]:
        """Get user's entry status for a jackpot"""
        try:
            entry = JackpotEntry.objects.get(jackpot=jackpot, user=user)
            
            predictions = []
            for prediction in entry.predictions.all().order_by('game__game_number'):
                predictions.append({
                    'game_number': prediction.game.game_number,
                    'event_name': str(prediction.game.event),
                    'predicted_outcome': prediction.predicted_odds.selection,
                    'is_correct': prediction.is_correct if prediction.is_settled else None,
                    'is_settled': prediction.is_settled
                })
            
            return {
                'entry_id': str(entry.id),
                'correct_predictions': entry.correct_predictions,
                'total_predictions': entry.total_predictions,
                'accuracy': entry.accuracy_percentage,
                'is_winner': entry.is_winner,
                'prize_won': str(entry.prize_won),
                'prize_tier': entry.prize_tier,
                'status': entry.status,
                'predictions': predictions
            }
            
        except JackpotEntry.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"Error getting user entry status: {e}")
            return {'error': str(e)}
