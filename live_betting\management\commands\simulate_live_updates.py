"""
Management command to simulate live betting updates for testing
"""

import time
import random
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.utils import timezone
from sports.models import Sport, Event, Market, Odds
from live_betting.services import LiveBettingService, OddsUpdateService, EventStatusService


class Command(BaseCommand):
    help = 'Simulate live betting updates for testing WebSocket functionality'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--duration',
            type=int,
            default=60,
            help='Duration to run simulation in seconds (default: 60)'
        )
        parser.add_argument(
            '--interval',
            type=int,
            default=5,
            help='Interval between updates in seconds (default: 5)'
        )
        parser.add_argument(
            '--event-id',
            type=str,
            help='Specific event ID to simulate (optional)'
        )
    
    def handle(self, *args, **options):
        duration = options['duration']
        interval = options['interval']
        event_id = options.get('event_id')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Starting live betting simulation for {duration} seconds '
                f'with {interval}s intervals'
            )
        )
        
        # Initialize services
        live_betting_service = LiveBettingService()
        odds_service = OddsUpdateService()
        event_service = EventStatusService()
        
        # Get events to simulate
        if event_id:
            try:
                events = [Event.objects.get(id=event_id, status='live')]
            except Event.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Event {event_id} not found or not live')
                )
                return
        else:
            events = Event.objects.filter(status='live')[:3]  # Limit to 3 events
        
        if not events:
            self.stdout.write(
                self.style.WARNING('No live events found. Creating test event...')
            )
            events = [self.create_test_event()]
        
        self.stdout.write(f'Simulating updates for {len(events)} event(s)')
        
        start_time = time.time()
        update_count = 0
        
        try:
            while time.time() - start_time < duration:
                for event in events:
                    # Simulate odds updates
                    self.simulate_odds_updates(event, odds_service)
                    
                    # Simulate event status updates
                    if random.random() < 0.3:  # 30% chance
                        self.simulate_event_status_update(event, event_service)
                    
                    update_count += 1
                
                self.stdout.write(f'Sent update batch {update_count}')
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.stdout.write('\nSimulation interrupted by user')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Simulation completed. Sent {update_count} update batches.'
            )
        )
    
    def create_test_event(self):
        """Create a test event for simulation"""
        sport, _ = Sport.objects.get_or_create(
            name='Football',
            defaults={'is_active': True}
        )

        event = Event.objects.create(
            home_team='Team A',
            away_team='Team B',
            sport=sport,
            start_time=timezone.now(),
            status='live'
        )

        # Create markets and odds
        market = Market.objects.create(
            name='Match Winner',
            market_type='1x2',
            event=event,
            is_active=True
        )

        Odds.objects.create(
            selection='Team A Win',
            odds_value=Decimal('2.10'),
            market=market,
            is_active=True
        )

        Odds.objects.create(
            selection='Draw',
            odds_value=Decimal('3.20'),
            market=market,
            is_active=True
        )

        Odds.objects.create(
            selection='Team B Win',
            odds_value=Decimal('3.50'),
            market=market,
            is_active=True
        )

        self.stdout.write(f'Created test event: {event.home_team} vs {event.away_team}')
        return event
    
    def simulate_odds_updates(self, event, odds_service):
        """Simulate odds updates for an event"""
        markets = Market.objects.filter(event=event, is_active=True)
        
        for market in markets:
            odds_list = list(Odds.objects.filter(market=market, is_active=True))
            
            if odds_list:
                # Randomly select some odds to update
                odds_to_update = random.sample(
                    odds_list, 
                    min(random.randint(1, len(odds_list)), len(odds_list))
                )
                
                odds_updates = []
                for odds in odds_to_update:
                    # Generate random odds change (-10% to +10%)
                    change_factor = 1 + (random.random() - 0.5) * 0.2
                    new_value = odds.odds_value * Decimal(str(change_factor))
                    new_value = max(new_value, Decimal('1.01'))  # Minimum odds
                    new_value = round(new_value, 2)
                    
                    odds_updates.append({
                        'id': str(odds.pk),
                        'value': str(new_value)
                    })
                
                if odds_updates:
                    odds_service.update_odds(str(market.pk), odds_updates)
    
    def simulate_event_status_update(self, event, event_service):
        """Simulate event status updates"""
        status_updates = {}

        # Simulate score updates
        home_score = event.home_score or 0
        away_score = event.away_score or 0

        # Randomly update score
        if random.random() < 0.5:  # 50% chance to update score
            if random.random() < 0.5:
                home_score += 1
            else:
                away_score += 1

            status_updates['home_score'] = home_score
            status_updates['away_score'] = away_score

        # Simulate match time
        if random.random() < 0.7:  # 70% chance to update time
            # Parse current match time or start from 0
            try:
                current_time = int(event.match_time.replace("'", "")) if event.match_time else 0
            except (ValueError, AttributeError):
                current_time = 0

            new_time = min(current_time + random.randint(1, 5), 90)  # Max 90 minutes
            status_updates['match_time'] = f"{new_time}'"

            # End match if time is up
            if new_time >= 90 and random.random() < 0.1:  # 10% chance to end
                status_updates['status'] = 'finished'

        if status_updates:
            event_service.update_event_status(str(event.id), status_updates)
