"""
Services for real-time live betting functionality
"""

import json
import logging
from decimal import Decimal
from datetime import datetime
from typing import Dict, List, Optional, Any
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.core.cache import cache
from django.utils import timezone
from django.db import transaction

logger = logging.getLogger(__name__)


class LiveBettingService:
    """Service for managing live betting operations and real-time updates"""
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def broadcast_odds_update(self, event_id: str, odds_data: Dict[str, Any]):
        """
        Broadcast odds update to all connected clients
        
        Args:
            event_id: Event ID for the odds update
            odds_data: Dictionary containing odds information
        """
        try:
            # Prepare the update data
            update_data = {
                'event_id': event_id,
                'odds': odds_data,
                'timestamp': timezone.now().isoformat(),
                'type': 'odds_update'
            }
            
            # Broadcast to general live odds room
            async_to_sync(self.channel_layer.group_send)(
                'live_odds',
                {
                    'type': 'odds_update',
                    'data': update_data
                }
            )
            
            # Broadcast to specific event room
            async_to_sync(self.channel_layer.group_send)(
                f'live_betting_{event_id}',
                {
                    'type': 'odds_change',
                    'data': update_data
                }
            )
            
            logger.info(f"Broadcasted odds update for event {event_id}")
            
        except Exception as e:
            logger.error(f"Error broadcasting odds update: {e}")
    
    def broadcast_event_update(self, event_id: str, event_data: Dict[str, Any]):
        """
        Broadcast event status update to connected clients
        
        Args:
            event_id: Event ID
            event_data: Dictionary containing event status information
        """
        try:
            update_data = {
                'event_id': event_id,
                'event_status': event_data,
                'timestamp': timezone.now().isoformat(),
                'type': 'event_update'
            }
            
            # Broadcast to specific event room
            async_to_sync(self.channel_layer.group_send)(
                f'live_betting_{event_id}',
                {
                    'type': 'event_update',
                    'data': update_data
                }
            )
            
            logger.info(f"Broadcasted event update for event {event_id}")
            
        except Exception as e:
            logger.error(f"Error broadcasting event update: {e}")
    
    def broadcast_bet_update(self, user_id: str, event_id: str, bet_data: Dict[str, Any]):
        """
        Broadcast bet update to specific user
        
        Args:
            user_id: User ID who placed the bet
            event_id: Event ID
            bet_data: Dictionary containing bet information
        """
        try:
            update_data = {
                'event_id': event_id,
                'bet_info': bet_data,
                'timestamp': timezone.now().isoformat(),
                'type': 'bet_update'
            }
            
            # Send to specific event room (will be filtered by user in consumer)
            async_to_sync(self.channel_layer.group_send)(
                f'live_betting_{event_id}',
                {
                    'type': 'bet_update',
                    'user_id': user_id,
                    'data': update_data
                }
            )
            
            logger.info(f"Broadcasted bet update for user {user_id} on event {event_id}")
            
        except Exception as e:
            logger.error(f"Error broadcasting bet update: {e}")
    
    def broadcast_event_finished(self, event_id: str, result_data: Dict[str, Any]):
        """
        Broadcast event finished notification
        
        Args:
            event_id: Event ID
            result_data: Dictionary containing event results
        """
        try:
            update_data = {
                'event_id': event_id,
                'results': result_data,
                'timestamp': timezone.now().isoformat(),
                'type': 'event_finished'
            }
            
            # Broadcast to specific event room
            async_to_sync(self.channel_layer.group_send)(
                f'live_betting_{event_id}',
                {
                    'type': 'event_finished',
                    'data': update_data
                }
            )
            
            logger.info(f"Broadcasted event finished for event {event_id}")
            
        except Exception as e:
            logger.error(f"Error broadcasting event finished: {e}")
    
    def get_active_connections_count(self) -> int:
        """Get total number of active WebSocket connections"""
        try:
            return cache.get('active_connections_count', 0)
        except Exception:
            return 0
    
    def get_event_connections_count(self, event_id: str) -> int:
        """Get number of active connections for specific event"""
        try:
            count_key = f"event_connection_count_{event_id}"
            return cache.get(count_key, 0)
        except Exception:
            return 0
    
    def get_connected_users_for_event(self, event_id: str) -> List[str]:
        """Get list of user IDs connected to specific event"""
        try:
            cache_key = f"event_connections_{event_id}"
            connections = cache.get(cache_key, set())
            return list(connections)
        except Exception:
            return []
    
    def is_user_online(self, user_id: str) -> bool:
        """Check if user is currently online"""
        try:
            return cache.get(f"user_online_{user_id}", False)
        except Exception:
            return False


class OddsUpdateService:
    """Service for managing odds updates and calculations"""
    
    def __init__(self):
        self.live_betting_service = LiveBettingService()
    
    def update_odds(self, market_id: str, odds_updates: List[Dict[str, Any]]):
        """
        Update odds and broadcast changes
        
        Args:
            market_id: Market ID
            odds_updates: List of odds updates with id, value, etc.
        """
        try:
            from sports.models import Market, Odds
            
            with transaction.atomic():
                market = Market.objects.select_related('event').get(id=market_id)
                
                # Update odds
                updated_odds = []
                for odds_update in odds_updates:
                    odds_id = odds_update.get('id')
                    new_value = Decimal(str(odds_update.get('value')))
                    
                    odds = Odds.objects.get(id=odds_id, market=market)
                    old_value = odds.value
                    
                    # Only update if value changed
                    if old_value != new_value:
                        odds.value = new_value
                        odds.updated_at = timezone.now()
                        odds.save()
                        
                        updated_odds.append({
                            'id': str(odds.id),
                            'name': odds.name,
                            'old_value': str(old_value),
                            'new_value': str(new_value),
                            'change_percentage': self.calculate_odds_change_percentage(old_value, new_value)
                        })
                
                # Broadcast updates if any odds changed
                if updated_odds:
                    odds_data = {
                        'market_id': market_id,
                        'market_name': market.name,
                        'updated_odds': updated_odds
                    }
                    
                    self.live_betting_service.broadcast_odds_update(
                        str(market.event.id), 
                        odds_data
                    )
                    
                    logger.info(f"Updated {len(updated_odds)} odds for market {market_id}")
                
        except Exception as e:
            logger.error(f"Error updating odds for market {market_id}: {e}")
    
    def calculate_odds_change_percentage(self, old_value: Decimal, new_value: Decimal) -> float:
        """Calculate percentage change in odds"""
        try:
            if old_value == 0:
                return 0.0
            change = ((new_value - old_value) / old_value) * 100
            return round(float(change), 2)
        except Exception:
            return 0.0
    
    def get_trending_odds(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get odds with significant recent changes"""
        try:
            # This would typically query recent odds changes from database
            # For now, return empty list as placeholder
            return []
        except Exception as e:
            logger.error(f"Error getting trending odds: {e}")
            return []


class EventStatusService:
    """Service for managing live event status updates"""
    
    def __init__(self):
        self.live_betting_service = LiveBettingService()
    
    def update_event_status(self, event_id: str, status_data: Dict[str, Any]):
        """
        Update event status and broadcast changes
        
        Args:
            event_id: Event ID
            status_data: Dictionary containing status information
        """
        try:
            from sports.models import Event
            
            with transaction.atomic():
                event = Event.objects.get(id=event_id)
                
                # Update event fields
                updated_fields = []
                if 'status' in status_data:
                    old_status = event.status
                    event.status = status_data['status']
                    if old_status != event.status:
                        updated_fields.append('status')
                
                if 'home_score' in status_data:
                    event.home_score = status_data['home_score']
                    updated_fields.append('home_score')

                if 'away_score' in status_data:
                    event.away_score = status_data['away_score']
                    updated_fields.append('away_score')

                if 'match_time' in status_data:
                    event.match_time = status_data['match_time']
                    updated_fields.append('match_time')
                
                if updated_fields:
                    event.updated_at = timezone.now()
                    event.save()
                    
                    # Broadcast update
                    self.live_betting_service.broadcast_event_update(event_id, {
                        'status': event.status,
                        'is_live': event.is_live,
                        'home_score': event.home_score,
                        'away_score': event.away_score,
                        'match_time': event.match_time,
                        'updated_fields': updated_fields
                    })
                    
                    logger.info(f"Updated event {event_id} fields: {updated_fields}")
                    
                    # Handle event finished
                    if event.status == 'finished':
                        self.handle_event_finished(event)
                
        except Exception as e:
            logger.error(f"Error updating event status for {event_id}: {e}")
    
    def handle_event_finished(self, event):
        """Handle event completion and bet settlement"""
        try:
            # Prepare event results for bet settlement
            event_results = {
                'final_score': getattr(event, 'score', None),
                'status': event.status,
                'finished_at': timezone.now().isoformat(),
                'winner': self._determine_winner(event),
                'total_goals': self._calculate_total_goals(event)
            }

            # Broadcast event finished
            self.live_betting_service.broadcast_event_finished(str(event.id), event_results)

            # Trigger automatic bet settlement
            settlement_service = BetSettlementService()
            settlement_result = settlement_service.settle_event_bets(str(event.id), event_results)

            if settlement_result['success']:
                logger.info(f"Event {event.id} finished and {settlement_result['bets_settled']} bets settled")
            else:
                logger.error(f"Failed to settle bets for event {event.id}: {settlement_result.get('error')}")

        except Exception as e:
            logger.error(f"Error handling event finished for {event.id}: {e}")

    def _determine_winner(self, event) -> str:
        """Determine the winner of an event based on score"""
        try:
            score = getattr(event, 'score', None)
            if not score:
                return 'unknown'

            # Parse score (e.g., "2-1")
            home_score, away_score = map(int, score.split('-'))

            if home_score > away_score:
                return 'home'
            elif away_score > home_score:
                return 'away'
            else:
                return 'draw'

        except (ValueError, AttributeError):
            return 'unknown'

    def _calculate_total_goals(self, event) -> int:
        """Calculate total goals from event score"""
        try:
            score = getattr(event, 'score', None)
            if not score:
                return 0

            home_score, away_score = map(int, score.split('-'))
            return home_score + away_score

        except (ValueError, AttributeError):
            return 0


class BetSettlementService:
    """Service for automatic bet settlement when events finish"""

    def __init__(self):
        self.live_betting_service = LiveBettingService()

    def settle_event_bets(self, event_id: str, results: Dict[str, Any]):
        """
        Settle all bets for a finished event

        Args:
            event_id: Event ID
            results: Dictionary containing event results and winning outcomes
        """
        try:
            from betting.models import Bet
            from sports.models import Event, Market, Odds

            with transaction.atomic():
                event = Event.objects.get(id=event_id)

                # Get all unsettled bets for this event
                unsettled_bets = Bet.objects.filter(
                    selections__market__event=event,
                    status='pending'
                ).distinct()

                settled_count = 0
                total_winnings = Decimal('0.00')

                for bet in unsettled_bets:
                    settlement_result = self._settle_individual_bet(bet, results)

                    if settlement_result['settled']:
                        settled_count += 1
                        if settlement_result['won']:
                            total_winnings += settlement_result['winnings']

                # Log settlement summary
                logger.info(f"Settled {settled_count} bets for event {event_id}, total winnings: {total_winnings}")

                # Broadcast settlement completion
                self.live_betting_service.broadcast_event_finished(event_id, {
                    'settlement_complete': True,
                    'bets_settled': settled_count,
                    'total_winnings': str(total_winnings),
                    'results': results
                })

                return {
                    'success': True,
                    'bets_settled': settled_count,
                    'total_winnings': total_winnings
                }

        except Exception as e:
            logger.error(f"Error settling bets for event {event_id}: {e}")
            return {'success': False, 'error': str(e)}

    def _settle_individual_bet(self, bet, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Settle an individual bet based on event results

        Args:
            bet: Bet instance to settle
            results: Event results dictionary

        Returns:
            dict: Settlement result with status and winnings
        """
        try:
            from betting.models import BetSelection

            # Check all selections in the bet
            all_selections_won = True
            settlement_details = []

            for selection in bet.selections.all():
                selection_result = self._evaluate_selection(selection, results)
                settlement_details.append(selection_result)

                if not selection_result['won']:
                    all_selections_won = False

            # Update bet status and calculate winnings
            if all_selections_won:
                bet.status = 'won'
                winnings = bet.potential_winnings

                # Credit user account
                self._credit_user_winnings(bet.user, winnings)

                # Update bet
                bet.actual_winnings = winnings
                bet.settled_at = timezone.now()
                bet.save()

                # Send notification
                self._send_bet_settlement_notification(bet, 'won', winnings)

                return {
                    'settled': True,
                    'won': True,
                    'winnings': winnings,
                    'details': settlement_details
                }
            else:
                bet.status = 'lost'
                bet.actual_winnings = Decimal('0.00')
                bet.settled_at = timezone.now()
                bet.save()

                # Send notification
                self._send_bet_settlement_notification(bet, 'lost', Decimal('0.00'))

                return {
                    'settled': True,
                    'won': False,
                    'winnings': Decimal('0.00'),
                    'details': settlement_details
                }

        except Exception as e:
            logger.error(f"Error settling individual bet {bet.id}: {e}")
            return {'settled': False, 'error': str(e)}

    def _evaluate_selection(self, selection, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate if a bet selection won based on event results

        Args:
            selection: BetSelection instance
            results: Event results dictionary

        Returns:
            dict: Selection evaluation result
        """
        try:
            market = selection.market
            odds = selection.odds

            # Get market results from event results
            market_results = results.get('markets', {}).get(str(market.id))

            if not market_results:
                # If no specific market results, try to infer from general results
                market_results = self._infer_market_result(market, results)

            if not market_results:
                logger.warning(f"No results found for market {market.id}")
                return {'won': False, 'reason': 'No market results available'}

            # Check if this odds selection won
            winning_odds_ids = market_results.get('winning_odds', [])
            selection_won = str(odds.id) in [str(oid) for oid in winning_odds_ids]

            return {
                'won': selection_won,
                'market_id': str(market.id),
                'odds_id': str(odds.id),
                'odds_name': odds.name,
                'market_result': market_results
            }

        except Exception as e:
            logger.error(f"Error evaluating selection {selection.id}: {e}")
            return {'won': False, 'error': str(e)}

    def _infer_market_result(self, market, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Infer market result from general event results

        Args:
            market: Market instance
            results: Event results dictionary

        Returns:
            dict: Inferred market result
        """
        try:
            market_name = market.name.lower()
            final_score = results.get('final_score', '')

            # Handle "Match Winner" market
            if 'winner' in market_name or 'match result' in market_name:
                if final_score:
                    # Parse score (e.g., "2-1")
                    try:
                        home_score, away_score = map(int, final_score.split('-'))

                        if home_score > away_score:
                            # Home team won
                            winning_odds = market.odds_set.filter(
                                name__icontains='home'
                            ).first()
                        elif away_score > home_score:
                            # Away team won
                            winning_odds = market.odds_set.filter(
                                name__icontains='away'
                            ).first()
                        else:
                            # Draw
                            winning_odds = market.odds_set.filter(
                                name__icontains='draw'
                            ).first()

                        if winning_odds:
                            return {
                                'winning_odds': [str(winning_odds.id)],
                                'inferred': True,
                                'basis': f'Score: {final_score}'
                            }
                    except ValueError:
                        pass

            # Handle "Over/Under" markets
            elif 'over' in market_name or 'under' in market_name:
                if final_score:
                    try:
                        home_score, away_score = map(int, final_score.split('-'))
                        total_goals = home_score + away_score

                        # Extract threshold from market name (e.g., "Over/Under 2.5")
                        import re
                        threshold_match = re.search(r'(\d+\.?\d*)', market_name)
                        if threshold_match:
                            threshold = float(threshold_match.group(1))

                            if total_goals > threshold:
                                winning_odds = market.odds_set.filter(
                                    name__icontains='over'
                                ).first()
                            else:
                                winning_odds = market.odds_set.filter(
                                    name__icontains='under'
                                ).first()

                            if winning_odds:
                                return {
                                    'winning_odds': [str(winning_odds.id)],
                                    'inferred': True,
                                    'basis': f'Total goals: {total_goals}, Threshold: {threshold}'
                                }
                    except ValueError:
                        pass

            return None

        except Exception as e:
            logger.error(f"Error inferring market result for {market.id}: {e}")
            return None

    def _credit_user_winnings(self, user, amount: Decimal):
        """Credit winnings to user account"""
        try:
            user.balance += amount
            user.save()

            # Create transaction record
            from payments.models import Transaction
            Transaction.objects.create(
                user=user,
                transaction_type='credit',
                amount=amount,
                status='completed',
                description='Bet winnings',
                metadata={'source': 'bet_settlement'}
            )

            logger.info(f"Credited {amount} to user {user.id} for bet winnings")

        except Exception as e:
            logger.error(f"Error crediting winnings to user {user.id}: {e}")

    def _send_bet_settlement_notification(self, bet, result: str, winnings: Decimal):
        """Send bet settlement notification to user"""
        try:
            # This would integrate with a notification system
            # For now, just log the notification
            message = f"Bet #{bet.id} {result}"
            if result == 'won':
                message += f" - Winnings: KES {winnings}"

            logger.info(f"Notification for user {bet.user.id}: {message}")

            # Could also broadcast via WebSocket to user if they're online
            if hasattr(self, 'live_betting_service'):
                user_id = str(bet.user.id)
                if self.live_betting_service.is_user_online(user_id):
                    # Send real-time notification
                    pass

        except Exception as e:
            logger.error(f"Error sending bet settlement notification: {e}")

    def get_settlement_summary(self, event_id: str) -> Dict[str, Any]:
        """Get settlement summary for an event"""
        try:
            from betting.models import Bet
            from sports.models import Event

            event = Event.objects.get(id=event_id)

            # Get all bets for this event
            all_bets = Bet.objects.filter(
                selections__market__event=event
            ).distinct()

            won_bets = all_bets.filter(status='won')
            lost_bets = all_bets.filter(status='lost')
            pending_bets = all_bets.filter(status='pending')

            total_winnings = sum(bet.actual_winnings or Decimal('0.00') for bet in won_bets)
            total_stakes = sum(bet.stake for bet in all_bets)

            return {
                'event_id': event_id,
                'event_name': f"{event.home_team} vs {event.away_team}",
                'total_bets': all_bets.count(),
                'won_bets': won_bets.count(),
                'lost_bets': lost_bets.count(),
                'pending_bets': pending_bets.count(),
                'total_stakes': str(total_stakes),
                'total_winnings': str(total_winnings),
                'settlement_complete': pending_bets.count() == 0
            }

        except Exception as e:
            logger.error(f"Error getting settlement summary for event {event_id}: {e}")
            return {'error': str(e)}
