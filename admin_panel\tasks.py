"""
Celery tasks for admin panel - system monitoring and compliance
"""

from celery import shared_task
from django.utils import timezone
from django.db.models import Count, Sum, Avg
from datetime import timedelta
from decimal import Decimal
import logging

from .models import SystemMonitoring, SuspiciousActivity, SystemAlert, AuditLog
from .services import (
    ComplianceMonitoringService, SystemMonitoringService, 
    AdminAnalyticsService, AuditTrailService
)
from accounts.models import CustomUser
from betting.models import Bet
from payments.models import Transaction

logger = logging.getLogger(__name__)


@shared_task
def monitor_system_health():
    """
    Periodic task to monitor system health metrics
    """
    try:
        monitoring_service = SystemMonitoringService()
        
        # Monitor active users (last hour)
        one_hour_ago = timezone.now() - timedelta(hours=1)
        active_users = CustomUser.objects.filter(last_login__gte=one_hour_ago).count()
        monitoring_service.record_system_metric(
            'active_users',
            Decimal(str(active_users)),
            'count'
        )
        
        # Monitor betting volume (last hour)
        recent_bets = Bet.objects.filter(placed_at__gte=one_hour_ago).count()
        monitoring_service.record_system_metric(
            'bet_volume',
            Decimal(str(recent_bets)),
            'count'
        )
        
        # Monitor transaction volume (last hour)
        recent_transactions = Transaction.objects.filter(created_at__gte=one_hour_ago).count()
        monitoring_service.record_system_metric(
            'transaction_volume',
            Decimal(str(recent_transactions)),
            'count'
        )
        
        # Monitor error rate (high-risk audit logs in last hour)
        error_logs = AuditLog.objects.filter(
            created_at__gte=one_hour_ago,
            risk_level__in=['high', 'critical']
        ).count()
        monitoring_service.record_system_metric(
            'error_rate',
            Decimal(str(error_logs)),
            'count',
            warning_threshold=Decimal('10'),
            critical_threshold=Decimal('25')
        )
        
        logger.info("System health monitoring completed successfully")
        return f"System health monitoring completed. Recorded {4} metrics."
        
    except Exception as e:
        logger.error(f"Error in system health monitoring: {e}")
        return f"Error in system health monitoring: {str(e)}"


@shared_task
def run_compliance_scan():
    """
    Periodic task to run compliance monitoring and detect suspicious activities
    """
    try:
        compliance_service = ComplianceMonitoringService()
        
        # Run suspicious activity detection
        detected_activities = compliance_service.detect_suspicious_activities()
        
        # Create records for new activities
        new_activities = 0
        for activity_data in detected_activities:
            # Check if similar activity already exists
            existing = SuspiciousActivity.objects.filter(
                user_id=activity_data['user_id'],
                activity_type=activity_data['activity_type'],
                status__in=['detected', 'investigating']
            ).exists()
            
            if not existing:
                compliance_service.create_suspicious_activity_record(activity_data)
                new_activities += 1
        
        logger.info(f"Compliance scan completed. {new_activities} new suspicious activities detected.")
        return f"Compliance scan completed. {len(detected_activities)} activities detected, {new_activities} new records created."
        
    except Exception as e:
        logger.error(f"Error in compliance scan: {e}")
        return f"Error in compliance scan: {str(e)}"


@shared_task
def generate_daily_report():
    """
    Generate daily administrative report
    """
    try:
        analytics_service = AdminAnalyticsService()
        
        # Generate dashboard stats for yesterday
        yesterday = timezone.now() - timedelta(days=1)
        stats = analytics_service.get_dashboard_stats(date_range=1)
        
        # Create summary report
        report_data = {
            'date': yesterday.date().isoformat(),
            'users': stats.get('users', {}),
            'betting': stats.get('betting', {}),
            'financial': stats.get('financial', {}),
            'system': stats.get('system', {}),
            'generated_at': timezone.now().isoformat()
        }
        
        # Log the report generation
        audit_service = AuditTrailService()
        audit_service.log_action(
            action_type='system_event',
            description='Daily administrative report generated',
            new_values=report_data,
            risk_level='low'
        )
        
        logger.info("Daily administrative report generated successfully")
        return f"Daily report generated for {yesterday.date()}"
        
    except Exception as e:
        logger.error(f"Error generating daily report: {e}")
        return f"Error generating daily report: {str(e)}"


@shared_task
def cleanup_old_audit_logs():
    """
    Clean up old audit logs (older than 1 year)
    """
    try:
        one_year_ago = timezone.now() - timedelta(days=365)
        
        old_logs = AuditLog.objects.filter(created_at__lt=one_year_ago)
        count = old_logs.count()
        old_logs.delete()
        
        logger.info(f"Cleaned up {count} old audit logs")
        return f"Cleaned up {count} old audit logs"
        
    except Exception as e:
        logger.error(f"Error cleaning up audit logs: {e}")
        return f"Error cleaning up audit logs: {str(e)}"


@shared_task
def cleanup_old_monitoring_data():
    """
    Clean up old monitoring data (older than 90 days)
    """
    try:
        ninety_days_ago = timezone.now() - timedelta(days=90)
        
        old_metrics = SystemMonitoring.objects.filter(recorded_at__lt=ninety_days_ago)
        count = old_metrics.count()
        old_metrics.delete()
        
        logger.info(f"Cleaned up {count} old monitoring metrics")
        return f"Cleaned up {count} old monitoring metrics"
        
    except Exception as e:
        logger.error(f"Error cleaning up monitoring data: {e}")
        return f"Error cleaning up monitoring data: {str(e)}"


@shared_task
def process_high_risk_activities():
    """
    Process high-risk suspicious activities and create alerts
    """
    try:
        # Get unprocessed high-risk activities
        high_risk_activities = SuspiciousActivity.objects.filter(
            severity_score__gte=Decimal('8.0'),
            status='detected'
        )
        
        processed_count = 0
        for activity in high_risk_activities:
            # Create critical system alert
            SystemAlert.objects.create(
                alert_type='security',
                severity='critical',
                title=f'Critical Suspicious Activity: {activity.activity_type}',
                message=f'User {getattr(activity.user, "phone_number", "Unknown")} flagged for {activity.activity_type} with risk score {activity.severity_score}',
                source_system='compliance',
                metadata={
                    'suspicious_activity_id': str(activity.id),
                    'user_id': str(getattr(activity.user, 'id', 'Unknown')),
                    'activity_type': activity.activity_type,
                    'severity_score': float(activity.severity_score)
                }
            )
            
            # Update activity status
            activity.status = 'investigating'
            activity.investigated_at = timezone.now()
            activity.save(update_fields=['status', 'investigated_at'])
            
            processed_count += 1
        
        logger.info(f"Processed {processed_count} high-risk suspicious activities")
        return f"Processed {processed_count} high-risk suspicious activities"
        
    except Exception as e:
        logger.error(f"Error processing high-risk activities: {e}")
        return f"Error processing high-risk activities: {str(e)}"


@shared_task
def update_system_statistics():
    """
    Update system-wide statistics and cache them
    """
    try:
        from django.core.cache import cache
        
        # Calculate current statistics
        stats = {
            'total_users': CustomUser.objects.count(),
            'active_users_24h': CustomUser.objects.filter(
                last_login__gte=timezone.now() - timedelta(hours=24)
            ).count(),
            'total_bets_24h': Bet.objects.filter(
                placed_at__gte=timezone.now() - timedelta(hours=24)
            ).count(),
            'total_transactions_24h': Transaction.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).count(),
            'active_alerts': SystemAlert.objects.filter(status='active').count(),
            'suspicious_activities_pending': SuspiciousActivity.objects.filter(
                status__in=['detected', 'investigating']
            ).count(),
            'last_updated': timezone.now().isoformat()
        }
        
        # Cache the statistics for 1 hour
        cache.set('admin_system_stats', stats, 3600)
        
        logger.info("System statistics updated and cached")
        return f"System statistics updated: {len(stats)} metrics cached"
        
    except Exception as e:
        logger.error(f"Error updating system statistics: {e}")
        return f"Error updating system statistics: {str(e)}"


@shared_task
def check_system_alerts():
    """
    Check for system conditions that should trigger alerts
    """
    try:
        alerts_created = 0
        
        # Check for too many failed login attempts
        one_hour_ago = timezone.now() - timedelta(hours=1)
        failed_logins = AuditLog.objects.filter(
            action_type='login',
            created_at__gte=one_hour_ago,
            description__icontains='failed'
        ).count()
        
        if failed_logins > 50:  # Configurable threshold
            SystemAlert.objects.create(
                alert_type='security',
                severity='warning',
                title='High Number of Failed Login Attempts',
                message=f'{failed_logins} failed login attempts in the last hour',
                source_system='monitoring',
                metadata={'failed_login_count': failed_logins}
            )
            alerts_created += 1
        
        # Check for unusual betting volume
        recent_bets = Bet.objects.filter(placed_at__gte=one_hour_ago).count()
        avg_hourly_bets = Bet.objects.filter(
            placed_at__gte=timezone.now() - timedelta(days=7)
        ).count() / (7 * 24)  # Average per hour over last week
        
        if recent_bets > avg_hourly_bets * 3:  # 3x normal volume
            SystemAlert.objects.create(
                alert_type='system',
                severity='warning',
                title='Unusual Betting Volume Detected',
                message=f'Current hourly betting volume ({recent_bets}) is {recent_bets/avg_hourly_bets:.1f}x normal',
                source_system='monitoring',
                metadata={
                    'current_volume': recent_bets,
                    'average_volume': avg_hourly_bets,
                    'multiplier': recent_bets / avg_hourly_bets if avg_hourly_bets > 0 else 0
                }
            )
            alerts_created += 1
        
        logger.info(f"System alert check completed. {alerts_created} new alerts created.")
        return f"System alert check completed. {alerts_created} new alerts created."
        
    except Exception as e:
        logger.error(f"Error checking system alerts: {e}")
        return f"Error checking system alerts: {str(e)}"


@shared_task
def archive_resolved_activities():
    """
    Archive resolved suspicious activities older than 30 days
    """
    try:
        thirty_days_ago = timezone.now() - timedelta(days=30)
        
        # Find resolved activities older than 30 days
        old_resolved = SuspiciousActivity.objects.filter(
            status='resolved',
            resolved_at__lt=thirty_days_ago
        )
        
        count = old_resolved.count()
        
        # In a real implementation, you might move these to an archive table
        # For now, we'll just mark them as archived by updating evidence_data
        for activity in old_resolved:
            evidence_data = activity.evidence_data or {}
            evidence_data.update({'archived': True, 'archived_at': timezone.now().isoformat()})
            activity.evidence_data = evidence_data
            activity.save(update_fields=['evidence_data'])
        
        logger.info(f"Archived {count} resolved suspicious activities")
        return f"Archived {count} resolved suspicious activities"
        
    except Exception as e:
        logger.error(f"Error archiving resolved activities: {e}")
        return f"Error archiving resolved activities: {str(e)}"
